import json
import os
import glob
import random
import matplotlib.pyplot as plt
import matplotlib.patches as patches
from PIL import Image
import sys

def load_prediction_data(json_path):
    """Load prediction data from JSON file"""
    try:
        with open(json_path, 'r') as f:
            data = json.load(f)
        
        # Extract relevant information
        image_info = data['images'][0]
        annotation = data['annotations'][0]
        
        filename = image_info['file_name']
        height = image_info['height']
        width = image_info['width']
        color = annotation['captions'][0]['color']
        
        return {
            'filename': filename,
            'height': height,
            'width': width,
            'predicted_color': color,
            'json_path': json_path
        }
    except Exception as e:
        print(f"Error loading {json_path}: {e}")
        return None

def find_corresponding_image(json_path, data_dir):
    """Find the corresponding image file for a JSON prediction"""
    # Extract batch name from JSON path
    batch_name = os.path.basename(os.path.dirname(json_path))
    
    # Get the image filename from JSON
    with open(json_path, 'r') as f:
        data = json.load(f)
    image_filename = data['images'][0]['file_name']
    
    # Construct image path
    image_path = os.path.join(data_dir, batch_name, image_filename)
    
    if os.path.exists(image_path):
        return image_path
    else:
        print(f"Image not found: {image_path}")
        return None

def visualize_predictions(batch_name=None, num_images=10, data_dir="data", output_dir="outputs"):
    """Visualize random predictions from a batch or all batches"""
    
    # Find JSON files
    if batch_name:
        json_pattern = os.path.join(output_dir, batch_name, "*.json")
        title_suffix = f" - {batch_name}"
    else:
        json_pattern = os.path.join(output_dir, "*", "*.json")
        title_suffix = " - All Batches"
    
    json_files = glob.glob(json_pattern)
    
    if not json_files:
        print(f"No JSON files found in pattern: {json_pattern}")
        return
    
    print(f"Found {len(json_files)} JSON files")
    
    # Randomly select files
    selected_files = random.sample(json_files, min(num_images, len(json_files)))
    
    # Create visualization
    fig, axes = plt.subplots(2, 5, figsize=(20, 8))
    fig.suptitle(f'Car Color Predictions{title_suffix}', fontsize=16, fontweight='bold')
    
    axes = axes.flatten()
    
    for i, json_path in enumerate(selected_files):
        # Load prediction data
        pred_data = load_prediction_data(json_path)
        if not pred_data:
            continue
        
        # Find corresponding image
        image_path = find_corresponding_image(json_path, data_dir)
        if not image_path:
            continue
        
        try:
            # Load and display image
            image = Image.open(image_path)
            axes[i].imshow(image)
            axes[i].axis('off')
            
            # Add prediction text
            color = pred_data['predicted_color']
            axes[i].set_title(f"Predicted: {color}", 
                            fontsize=12, fontweight='bold', 
                            color='darkblue', pad=10)
            
            # Add image info as subtitle
            axes[i].text(0.5, -0.1, f"{pred_data['width']}x{pred_data['height']}", 
                        transform=axes[i].transAxes, ha='center', 
                        fontsize=8, color='gray')
            
            # Add colored border based on prediction
            color_map = {
                'White': 'lightgray',
                'Black': 'black',
                'Gray': 'gray',
                'Silver': 'silver',
                'Red': 'red',
                'Blue': 'blue',
                'Green': 'green',
                'Yellow': 'yellow',
                'Brown': 'brown',
                'Orange': 'orange',
                'Purple': 'purple',
                'Pink': 'pink'
            }
            
            border_color = color_map.get(color, 'blue')
            rect = patches.Rectangle((0, 0), 1, 1, linewidth=3, 
                                   edgecolor=border_color, facecolor='none',
                                   transform=axes[i].transAxes)
            axes[i].add_patch(rect)
            
        except Exception as e:
            print(f"Error processing {image_path}: {e}")
            axes[i].text(0.5, 0.5, f"Error loading\n{os.path.basename(image_path)}", 
                        transform=axes[i].transAxes, ha='center', va='center')
            axes[i].set_title("Error", color='red')
    
    # Hide unused subplots
    for i in range(len(selected_files), len(axes)):
        axes[i].axis('off')
    
    plt.tight_layout()
    
    # Save visualization
    if batch_name:
        save_path = f"visualization_{batch_name}.png"
    else:
        save_path = "visualization_all_batches.png"
    
    plt.savefig(save_path, dpi=150, bbox_inches='tight')
    print(f"Visualization saved as: {save_path}")
    
    # Show plot
    plt.show()

def get_color_statistics(batch_name=None, output_dir="outputs"):
    """Get statistics of predicted colors"""
    
    if batch_name:
        json_pattern = os.path.join(output_dir, batch_name, "*.json")
    else:
        json_pattern = os.path.join(output_dir, "*", "*.json")
    
    json_files = glob.glob(json_pattern)
    
    if not json_files:
        print(f"No JSON files found in pattern: {json_pattern}")
        return
    
    color_counts = {}
    total_files = 0
    
    for json_path in json_files:
        pred_data = load_prediction_data(json_path)
        if pred_data:
            color = pred_data['predicted_color']
            color_counts[color] = color_counts.get(color, 0) + 1
            total_files += 1
    
    print(f"\nColor Statistics ({total_files} images):")
    print("=" * 40)
    
    # Sort by count (descending)
    sorted_colors = sorted(color_counts.items(), key=lambda x: x[1], reverse=True)
    
    for color, count in sorted_colors:
        percentage = (count / total_files) * 100
        print(f"{color:12}: {count:5} ({percentage:5.1f}%)")
    
    return color_counts

def main():
    """Main function"""
    import argparse
    
    parser = argparse.ArgumentParser(description='Visualize car color predictions')
    parser.add_argument('--batch', type=str, help='Specific batch name to visualize')
    parser.add_argument('--num', type=int, default=10, help='Number of images to show (default: 10)')
    parser.add_argument('--stats', action='store_true', help='Show color statistics')
    parser.add_argument('--data-dir', type=str, default='data', help='Data directory path')
    parser.add_argument('--output-dir', type=str, default='outputs', help='Output directory path')
    
    args = parser.parse_args()
    
    if args.stats:
        get_color_statistics(args.batch, args.output_dir)
    
    # Create visualization
    visualize_predictions(args.batch, args.num, args.data_dir, args.output_dir)

if __name__ == "__main__":
    # If run without arguments, show help and example
    if len(sys.argv) == 1:
        print("Car Color Prediction Visualizer")
        print("=" * 40)
        print("Usage examples:")
        print("  python visualize_predictions.py --batch color_sub_batch_76")
        print("  python visualize_predictions.py --num 20")
        print("  python visualize_predictions.py --stats")
        print("  python visualize_predictions.py --batch color_sub_batch_76 --stats")
        print("\nRunning default visualization (10 random images from all batches)...")
        visualize_predictions()
    else:
        main()
