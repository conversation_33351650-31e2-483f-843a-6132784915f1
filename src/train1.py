imgsdir = '/media/imerit/6353b67d-06d9-431f-94f3-c4d021585a38/projects_bkp/selex_car_color/retrain/selex_color/final2'

setname = 'selex_color'

from fastai.vision.all import *
import faitools as ft

# fields = DataBlock(blocks=(ImageBlock, CategoryBlock),
#    get_items=get_image_files,
#    get_y=parent_label,
#    splitter=RandomSplitter(valid_pct=0.2, seed=42),
#    item_tfms=RandomResizedCrop(224, min_scale=0.5),
#    batch_tfms=aug_transforms())

model = convnext_tiny # org : convnext_base
image_size = 128      # org : 320
start_n_epochs = 2    # org : 80

fields = DataBlock(blocks=(ImageBlock, CategoryBlock),
   get_items=get_image_files,
   get_y=parent_label,
   splitter=RandomSplitter(valid_pct=0.2, seed=42),
   item_tfms=RandomResizedCrop(image_size, min_scale=1.0),
   batch_tfms=aug_transforms(flip_vert=True))

dls = fields.dataloaders(imgsdir)
#dls.train.show_batch(max_n=8, nrows=2)


learn = cnn_learner(dls, model, metrics=error_rate)
lr_min, lr_steep = learn.lr_find(num_it=1000, suggest_funcs=(minimum, steep))
learn.fit_one_cycle(start_n_epochs, 3e-3, cbs=EarlyStoppingCallback(monitor='valid_loss', min_delta=0.0001, patience=4))

HT_model_path = setname + '_HT.pkl'
learn.export(HT_model_path)
df_out1 = ft.get_predictions_from_dir(imgsdir, HT_model_path, setname=setname + '_HT', mismatches_outdir_forcenew=True, SAVE_RESULTS=True)

# learn.unfreeze()
# learn.fit_one_cycle(4, lr_max=slice(0.002, 0.184))
# #learn.fit_one_cycle(4, lr_max=slice(1e-5, 5e-4))
# #learn.fit_one_cycle(4, lr_max=slice(0.002, 0.03))


# interp = ClassificationInterpretation.from_learner(learn)
# interp.plot_confusion_matrix()
# FT_model_path = setname + '_HT_finetuned.pkl'
# learn.export(FT_model_path)
# df_out1 = ft.get_predictions_from_dir(imgsdir, FT_model_path, setname=setname + '_FT', mismatches_outdir_forcenew=True, SAVE_RESULTS=True)

