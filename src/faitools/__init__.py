__all__ = ["process_prediction_results_from_df",
           "get_predictions_from_dir",
           ]

import os
import pandas as pd
import numpy as np
import shutil
import seaborn as sns
from sklearn.metrics import confusion_matrix
import matplotlib.pyplot as plt
from fastai.vision.all import load_learner
from tqdm import tqdm
import itworktools as iwt


def process_prediction_results_from_df(df_out, confmat_path=None, mismatches_outdir=None, mismatches_outdir_forcenew=False, gt_header='gt', pred_header='pred', filepath_header='fp', score_header='score'):    
    acc = (df_out[gt_header]==df_out[pred_header]).mean()
    print('Accuracy = '+str(acc))
    
    cf_matrix = confusion_matrix(df_out[gt_header], df_out[pred_header])
    cf_df = pd.DataFrame(cf_matrix)
    
    C = np.unique(np.hstack((df_out[gt_header], df_out[pred_header])))
    cf_df.index = C
    cf_df.columns = C
    
    
    plt.figure(figsize=(10, 10))
    sns.set(font_scale=1.5)
    
    sns.heatmap(cf_df,
                cmap='coolwarm',
                annot=True,
                fmt='.5g',
                vmax=200)
    
    plt.xlabel('Predicted',fontsize=22)
    plt.ylabel('Actual',fontsize=22)

    if confmat_path is not None:    
        fig = plt.gcf()
        fig.savefig(confmat_path)   # save the figure to file
        plt.close(fig)    
        print('Confusion matrix saved at - '+confmat_path)
        
    
    if mismatches_outdir is not None:
        if mismatches_outdir_forcenew:
            iwt.newmkdir(mismatches_outdir)
        else:
            iwt.mkdir(mismatches_outdir)
        
        mismatches_df = df_out[df_out[gt_header] != df_out[pred_header]]
        for index,row in mismatches_df.iterrows():    
            d = dict(row)
            
            scoreval = str(int(np.round(d[score_header]*100))).zfill(2)        
            id_ = d[gt_header] + '__' + d[pred_header] + '__' + scoreval
            src = d[filepath_header]
            
            fn = os.path.basename(src)
            out_fn = id_ + '__' + fn
            out_fp = os.path.join(mismatches_outdir, out_fn)    
            dst = out_fp
            shutil.copyfile(src, dst)
        print('Mismatches saved at - '+mismatches_outdir)
            
    return

def get_predictions_from_dir(dir0, modelpath, setname=None, mismatches_outdir_forcenew=False, SAVE_RESULTS=False):    
    if setname is None:
        setname = setname = os.path.basename(dir0) + '__' + os.path.splitext(os.path.basename(modelpath))[0]

    fpaths = iwt.list_image_files(dir0)
    
    learn = load_learner(modelpath, cpu=False)
    learn.dls.to(device='cuda')
    learn.model.to(device='cuda')
    
    info = []
    for fp in tqdm(fpaths):
        gt = os.path.basename(os.path.dirname(fp))
        out = learn.predict(fp)
        
        label = out[0]
        score = float(out[2][int(out[1])])    
    
        info.append({'fp':fp, 'gt':gt, 'pred':label, 'score':score})
    
    df_out = pd.DataFrame(info)
    print('\n')
    
    if SAVE_RESULTS:
        process_prediction_results_from_df(
            df_out, 
            confmat_path= setname + '_confmatrix.png', 
            mismatches_outdir=setname + '_mismatches', 
            mismatches_outdir_forcenew=mismatches_outdir_forcenew, 
            gt_header='gt', 
            pred_header='pred', 
            filepath_header='fp',
            score_header='score',
            )
        
    return df_out

