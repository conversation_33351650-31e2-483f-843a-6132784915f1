__all__ = ["list_image_files",
           "listsubdirs",
           "mkdir",
           "newmkdir",
           ]


import os
import cv2
import numpy as np
import pandas as pd
from glob import glob
from shutil import copyfile
import getpass
import fnmatch
import shutil
import ast
from sklearn.metrics import confusion_matrix
import seaborn as sn
import matplotlib.pyplot as plt
import warnings
from pprint import pprint
import ffmpeg
import matplotlib.image as mpimg

rename_split_by_U = lambda s : os.rename(s, os.path.join(os.path.dirname(s), os.path.basename(s).split('_')[0]))


def rmfiles(fpaths, print_info=False):
    count = 0
    for f in fpaths:
        if os.path.exists(f):
            os.remove(f)
            count += 1
            
    if print_info:
        print('Number of files removed = '+str(count))
    return

def annot_image_as_title(image_path, annotations, out_path, fontisize=8, top_margin=0.9):
    # eg. annotations = {'GT': 'Bus', 'Pred1': 'Van', 'Pred2': 'Bus'}
    image = mpimg.imread(image_path)
    
    title = '\n'.join([k+':'+v for k,v in annotations.items()])
    
    frame1 = plt.gca()
    frame1.axes.xaxis.set_ticklabels([])
    frame1.axes.yaxis.set_ticklabels([])
    
    plt.title(title, fontsize=fontisize)
    plt.imshow(image)
    plt.subplots_adjust(top=top_margin)
    plt.tight_layout()

    plt.savefig(out_path)
    return

def convert_avi_mp4(in_file, out_file):
    if os.path.exists(out_file):
        os.remove(out_file)
    
    stream = ffmpeg.input(in_file)
    stream = ffmpeg.output(stream, out_file)
    ffmpeg.run(stream)
    return

def save_framesdir_into_video(indir, REMOVE_AVI=True, fps=23):
    ID = os.path.basename(indir)
    outvideo_path = ID + '.avi'
    
    fpaths = sorted(list_image_files(indir))
    height, width = cv2.imread(fpaths[0]).shape[:2]
    img_video = cv2.VideoWriter(outvideo_path, 0, fps, (width,height))
    for iterID,fp in enumerate(fpaths):
        iterinfo(fpaths, iterID)
        im = cv2.imread(fp)
        img_video.write(im)

    img_video.release()
    out_mp4 = outvideo_path.replace('.avi','.mp4')
    
    convcmd = 'ffmpeg -i ' + outvideo_path + ' -c:v libx265 -x265-params lossless=1 -y ' + out_mp4    
    os.system(convcmd)

    if REMOVE_AVI:
        os.remove(outvideo_path)
    return out_mp4

def save_inference(f, dict1, savepath=None, x_start=20, y_start=20, y_offset = 40):
    val_dict = []
    for iterID,(k,v) in enumerate(dict1.items()):
        val_dict.append({'val':k + ': ' + v, 'pos':(x_start,y_start+y_offset*iterID)})
        
    im = draw_text_on_image(f, val_dict, show=False)
    
    if savepath is not None:
        cv2.imwrite(savepath, im)
        
    return

def draw_text_on_image(image, val_dict=None, show=False, help=False, fontsize=1, thickness=1):
    
    if help:
        print('Sample for txtdict : ')
        val_dict = [
            {
             'val':'GT: A man', 
             'pos':(20,20),
             },
            {
             'val':'Pred1: Go there!', 
             'pos':(20,40),
             },
            {
             'val':'Pred2: GNOOOO!!', 
             'pos':(20,60),
             },
            ]
        
        print('Sample val_dict : ')
        pprint(val_dict)
        return
        
    
    im = read_image_from_any_source(image)
    
    if val_dict is not None:
        for item in val_dict:
            lbl = item['val']
            pos = item['pos']
            cv2.putText(im, lbl, pos, cv2.FONT_HERSHEY_COMPLEX_SMALL, fontsize, (0,0,0), thickness, lineType=cv2.LINE_AA)
        
    if show:
        showim(im)
        
    return im

def sync_delete(maindir, fromdir, dryrun=False):
    main_fpaths = list_image_files(maindir)
    check_fpaths = list_image_files(fromdir)
    
    mask = np.isin(filepaths_to_filenames(main_fpaths), filepaths_to_filenames(check_fpaths))
    
    rmfpaths = np.array(main_fpaths)[mask]
    
    if dryrun:
        print('Total files to be removed : '+str(len(rmfpaths)))
    else:
        for f in rmfpaths:
            os.remove(f)
    return

def difftree(dir1, dir2, outdir, CHECK_SAME_SUBDIRS = True, PRINT_PROGRESS = False, dryrun=False):
    dir1_subdirs = os.listdir(dir1)
    dir2_subdirs = os.listdir(dir2)
    
    if CHECK_SAME_SUBDIRS:
        if sorted(dir1_subdirs) != sorted(dir2_subdirs):
            print('dir1_subdirs : '+str(dir1_subdirs))
            print('dir2_subdirs : '+str(dir2_subdirs))
            raise Exception("Common subdirs are not same!")
    
    if not dryrun:
        if outdir is None:
            outdir = dir1    
        else:
            mkdir(outdir)

    for s in dir1_subdirs:
        print('==> subdir name : '+s)
        indir_1 = os.path.join(dir1, s)
        indir_2 = os.path.join(dir2, s)
        
        if not os.path.exists(indir_2):
            print('Warning : Subdir does not exist : '+indir_2)
            continue
        
        fnames_1 = listfiles(indir_1)
        fnames_2 = listfiles(indir_2)
        
        fpaths_1 = filenames_to_filepaths(fnames_1, indir_1)
        
        src_fpaths = np.asarray(fpaths_1)[np.isin(fnames_1, fnames_2, invert=True)]
    
        outdir_i = os.path.join(outdir, s)
        
        if dryrun:
            print('Number of files to be copied : '+str(len(src_fpaths)))
        else:
            copyfiles_into_dir(src_fpaths, outdir_i, print_progress=PRINT_PROGRESS)
            
    return

def merge_dirs(dir1, dir2, outdir, force_new_outdir=False):
    s1 = listallsubdirs(dir1)
    s2 = listallsubdirs(dir2)
    
    common_dirnames = np.intersect1d(filepaths_to_filenames(s1), filepaths_to_filenames(s2))    
    if force_new_outdir:
        newmkdir(outdir)
    else:
        mkdir(outdir)
    
    for s in tqdm(common_dirnames):
        outdir_i = os.path.join(outdir, s)
        copyfiles_into_dir(listfiles(os.path.join(dir1, s), fullpath=True), outdir_i, print_progress=False)
        copyfiles_into_dir(listfiles(os.path.join(dir2, s), fullpath=True), outdir_i, print_progress=False)

    return
    
def rand_split(d, val_ratio=0.1):
    fpaths = list_image_files(d)
    fpaths = np.array(fpaths)
    
    L = len(fpaths)
    randidxs = np.random.permutation(L)
    
    fpaths = fpaths[randidxs]
    
    val_len = rint(L*val_ratio)
    
    train_fpaths = fpaths[val_len:]
    val_fpaths = fpaths[:val_len]
    label = os.path.basename(d)
    return train_fpaths, val_fpaths, label

def split_tree(indir, outdir=None, ratio=0.1):
    if outdir is None:
        main_outdir = indir + '_split_' + str(ratio)
    newmkdir(main_outdir)
    
    train_outdir = os.path.join(main_outdir, 'train')
    val_outdir = os.path.join(main_outdir, 'val')
    
    subdirs = listsubdirs(indir)
    for d in subdirs:
        train_fpaths, val_fpaths, label = rand_split(d, val_ratio=ratio)
        copyfiles_into_dir(train_fpaths, os.path.join(train_outdir, label), print_progress=False)
        copyfiles_into_dir(val_fpaths, os.path.join(val_outdir, label), print_progress=False)
    return
    
def diffdirs_save(indir1, indir2, outdir, ext):
    copyfiles_into_dir(diffdirs(indir1, indir2,ext), outdir)
    return
    
def resize_images_by_factor(intree, outtree, factor=0.5):
    shutil.copytree(intree, outtree)
    fpaths = list_image_files(outtree)
    for iterID,fp in enumerate(fpaths):
        iterinfo(fpaths, iterID)        
        im = cv2.imread(fp, 0)
        H,W = im.shape
        newW, newH = rint(W*factor), rint(H*factor)
        cv2.imwrite(fp, cv2.resize(im, (newW, newH)), [cv2.IMWRITE_PNG_COMPRESSION, 0])
    return

def add_prefix_to_images(intree, prefix, DRYRUN=False):
    fpaths = list_image_files(intree)
    for fp in fpaths:
        fn = os.path.basename(fp)
        newfn = prefix + fn
        newfp = os.path.join(os.path.dirname(fp), newfn)
        
        if DRYRUN:
            print('================================================================')
            print('Original filepath : '+fp)
            print('New filepath      : '+newfp)
        else:
            os.rename(fp, newfp)
    return

def get_dir_counts(intree):
    subdirs = listsubdirs(intree)
    counts = {}
    for indir in subdirs:
        lbl = os.path.basename(indir)
        counts[lbl] = len(list_image_files(indir))
    return counts

def trashit_paths(fpaths_rm):
    for fp in fpaths_rm:
        #os.remove(fp)
        trashit(fp)
    return

def repeat_op_for_each_subdir(func, intree):
    sdirs = listsubdirs(intree)
    for t in sdirs:
        func(t)
    return

def gather_imgs_from_tree_to_dir(intree, outdir=None, print_progress=False):
    if outdir is None:
        outdir = intree + '_allimgs_gathered'
    
    fpaths = list_image_files(intree)
    copyfiles_into_dir(fpaths, outdir, print_progress=print_progress)
    return

### Q - Quit
### D - Delete
### Esc - Next
### Bckspace - Prev
# Display window
def DisplayWindow_for_cleanup():
    while(1):
        k = cv2.waitKey(33)
        #print(k)
        if k==81 or k==113:    # quit with Q/q
             cv2.destroyAllWindows()
             return -1
        elif k==8:    # Key Backspace
             cv2.destroyAllWindows()
             return 10
        elif k==68 or k==100:    # Key D/d
             cv2.destroyAllWindows()
             return 11
        elif k==27:    # Esc key
             cv2.destroyAllWindows()
             return 1
        else:  # normally -1 returned,so don't print it
            continue

def cleanup_with_slideshow(d):
    fpaths = np.array(list_image_files(d))
    N = len(fpaths)
    iterID = 0
    while iterID<N:
        fp = fpaths[iterID]
        print(os.path.basename(fp))
        im = cv2.imread(fp)
        image_show_FS('img', im)
        k = DisplayWindow_for_cleanup()
        if k==-1:
            break
        elif k==11:
            os.remove(fp)
            fpaths = np.delete(fpaths, iterID)
            N -= 1
            continue
        elif k==10:
            iterID = max(0,iterID-1)
            continue
        else:
            pass
        iterID += 1
                
def get_imglabels(indir):
    return np.array([get_label(fp) for fp in list_image_files(indir)])

def get_label(fp):
    return os.path.basename(os.path.dirname(fp))
    
def print_imgs_count(tree):
    print('Number of images : '+str(len(list_image_files(tree))))

def setup_outtree(intree, outtree):
    newmkdir(outtree)
    subdirs = listallsubdirs_withimgs(intree)    
    out = []
    for s in subdirs:
        in1_ = s
        in2_ = s.replace(intree, outtree)
        mkdir(in2_)
        out.append([in1_, in2_])
    return out

def listallsubdirs_withimgs(d1):
    out = []
    for in_ in listallsubdirs(d1):
        if len(listfiles(in_))>0:
            out.append(in_)
    return out

def copyfiles_based_on_match_fromtwodirs_into_third_tree(in1tree, in2tree, outtree, ext, invert=False):
    subdirs = listallsubdirs_withimgs(in1tree)    
    for s in subdirs:
        in1_ = s
        in2_ = s.replace(in1tree, in2tree)
        out_ = s.replace(in1tree, outtree)
        copyfiles_based_on_match_fromtwodirs_into_third(in1_, in2_, out_, ext=ext, invert=invert)
        
    print('Number of images in in1_ : '+str(len(list_image_files(in1tree))))
    print('Number of images in in2_ : '+str(len(list_image_files(in2tree))))
    print('Number of images in out_ : '+str(len(list_image_files(outtree))))
    return

def trashit(fp):
    src = fp
    trashdir = '/home/<USER>/.local/share/Trash/files'
    dst = os.path.join(trashdir, os.path.basename(src))
    shutil.move(src, dst)
    return

def remove_empty_subdirs(dir1):
    for d in listallsubdirs(dir1):
        if len(listfiles(d))==0:
            try:
                os.rmdir(d)
            except:
                pass
    return

def compare_trees_native(t1, t2, excl_t1=True, excl_t2=True, common=True):
    outtree_excl_t1 = t1 + '_' + os.path.basename(t2) + '_diff' + '_excl_dir1'
    outtree_excl_t2 = t1 + '_' + os.path.basename(t2) + '_diff' + '_excl_dir2'
    outtree_common  = t1 + '_' + os.path.basename(t2) + '_diff' + '_common'
    
    all_labels = np.union1d(filepaths_to_filenames(listsubdirs(t1)), filepaths_to_filenames(listsubdirs(t2)))  
    for iterID,l in enumerate(all_labels):
        iterinfo(all_labels, iterID)
        dir1 = os.path.join(t1, l)
        dir2 = os.path.join(t2, l)
        
        outdir1, outdir2, outdir3 = None, None, None
        if excl_t1:
            outdir1 = os.path.join(outtree_excl_t1, l)

        if excl_t2:
            outdir2 = os.path.join(outtree_excl_t2, l)

        if common:
            outdir3 = os.path.join(outtree_common, l)

        study_diff_between_twodirs(dir1, dir2, excl_dir1=outdir1, excl_dir2=outdir2, common_dir=outdir3, PRINT_PROGRESS=False)
    return

def compare_trees(tree1, tree2, label, outtree, SAVE_COMMON=False, REMOVE_EMPTY_DIRS=False):
    main_indir1 = os.path.join(tree1, label)
    main_indir2 = os.path.join(tree2, label)
    
    subdirs1 = listsubdirs(main_indir1)
    subdirs2 = listsubdirs(main_indir2)
    
    mkdir(outtree)
    
    outdir1 = os.path.join(outtree, 'excl_dir1')
    outdir2 = os.path.join(outtree, 'excl_dir2')
    outdir3 = os.path.join(outtree, 'common')
    
    for (d1,d2) in zip(subdirs1, subdirs2):        
        if os.path.basename(d1) != os.path.basename(d2):
            raise Exception('Bad!')
            
        lbl = os.path.basename(d1)
        
        outdir1_label = os.path.join( os.path.join(outdir1, label), lbl )
        outdir2_label = os.path.join( os.path.join(outdir2, label), lbl )
        if SAVE_COMMON:
            outdir3_label = os.path.join( os.path.join(outdir3, label), lbl )
        else:
            outdir3_label = None
        
        study_diff_between_twodirs(d1, d2, outdir1_label, outdir2_label, outdir3_label, PRINT_PROGRESS=False)
        
        
    if REMOVE_EMPTY_DIRS:
        remove_empty_subdirs(outtree)
    return

def study_diff_between_twodirs(dir1, dir2, excl_dir1=None, excl_dir2=None, common_dir=None, PRINT_PROGRESS=False):
    ## With helper :
    # outdir_1, outdir_2, outdir_3 = study_diff_between_twodirs_gendirs(d1, d2, out_tree)

    fn1 = listfiles(dir1)
    fn2 = listfiles(dir2)
    
    if excl_dir1 is not None:
        copyfiles_into_dir(filenames_to_filepaths(np.setdiff1d(fn1, fn2), dir1), excl_dir1, print_progress=PRINT_PROGRESS)
    if excl_dir2 is not None:
        copyfiles_into_dir(filenames_to_filepaths(np.setdiff1d(fn2, fn1), dir2), excl_dir2, print_progress=PRINT_PROGRESS)    
    if common_dir is not None:
        copyfiles_into_dir(filenames_to_filepaths(np.intersect1d(fn1, fn2), dir1), common_dir, print_progress=PRINT_PROGRESS)
    return

def study_diff_between_twodirs_gendirs(d1, d2, out_tree):
    mkdir(out_tree)
    outdir_1 = os.path.join(out_tree, 'exclusive_in_dir1')
    outdir_2 = os.path.join(out_tree, 'exclusive_in_dir2')
    outdir_3 = os.path.join(out_tree, 'common')
    return outdir_1, outdir_2, outdir_3

def add_prefix_to_filepath(filepath, prefix):
    return os.path.join(os.path.dirname(filepath), prefix + os.path.basename(filepath))
    
def base_dirname(fp):
    return os.path.basename(os.path.dirname(fp))    

def add_postfix(filepath, postfix='', ext=None):
    dn = os.path.dirname(filepath)
    fn = os.path.basename(filepath)
    splits = fn.split('.')
    fnwoext = '.'.join(splits[:-1])
    
    if ext is None:
        ext = splits[-1]
        
    outfp = os.path.join(dn, fnwoext + postfix + '.' + ext)
    return outfp

def setdiff_save(bigger_dir, smaller_dir, output_dir):
    fnames_bigger_dir = listfiles(bigger_dir)
    fnames_smaller_dir = listfiles(smaller_dir)
    excl_fnames = np.setdiff1d(fnames_bigger_dir, fnames_smaller_dir)
    excl_fpaths = filenames_to_filepaths(excl_fnames, bigger_dir)
    copyfiles_into_dir(excl_fpaths, output_dir)
    return

def close_all_figures():
    plt.close("all")

def add_filename_from_filepath(csv, filepath_header, filename_header, drop_filepath_header=False, overwrite=False):
    df = read_data_from_any_source(csv)
    if filepath_header not in df:
        raise Exception('Filepath header not present in given csv.')
    if filename_header in df and not overwrite:
        warnings.warn('Filename header already present in given csv. No changes done.')
        return
    df[filename_header] = [os.path.basename(fp) for fp in df[filepath_header]]
    df.drop([filepath_header], axis=1, inplace=True)
    df.to_csv(csv, index=False)
    return 

def compare_data_on_metrics(gt_data, in_data, metric_func, sort=True):
    d = {}
    d_cum = {}
    for (k,v) in in_data.items():
        p_ed1 = metric_func(gt_data, v)
        if sort:
            d[k] = np.sort(p_ed1)
        else:
            d[k] = p_ed1
        d_cum[k] = np.sort(p_ed1).cumsum()/np.arange(1,len(p_ed1)+1)
    df1 = pd.DataFrame(d)
    df2 = pd.DataFrame(d_cum)
    return df1,df2
    
def compare_xy_against_GT_given_csvs_v3(in_params, sort=True, on_header = 'FN', show_plot=False):
    ## sample : in_params = [(gt_csv, 'LBP', 'GT'), (al_csv, 'LBPc', 'AL'), (dl_csv, 'LBP', 'DL')]
    df = merge_data_multiple(in_params)
    df = ast_literal_eval_inplace(df, headers=df.columns[1:])
    
    gt_data = df[in_params[0][2]]
    in_data_comp = {}
    for (csv, in_header, out_header) in in_params[1:]:
        in_data_comp[out_header] = df[out_header]
    
    eucl_dist = lambda a,b : np.linalg.norm(np.vstack(a) - np.vstack(b), axis=1)
    absX_diff = lambda a,b : np.abs(np.vstack(a)[:,0] - np.vstack(b)[:,0])
    absY_diff = lambda a,b : np.abs(np.vstack(a)[:,1] - np.vstack(b)[:,1])
    metrics = {'Eucliean distance':eucl_dist,
               'Abs X diff':absX_diff,
               'Abs Y diff':absY_diff}
    
    fig, axes = plt.subplots(nrows=len(metrics), ncols=2)
    df1_ms = {}
    df2_ms = {}
    for ii,(k,v) in enumerate(metrics.items()):    
        df1_m, df2_m = compare_data_on_metrics(gt_data, in_data=in_data_comp, metric_func=v, sort=sort)
        df1_ms[k] = df1_m
        df2_ms[k] = df2_m
        df1_m.plot(ax=axes[ii,0])
        df2_m.plot(ax=axes[ii,1])
        axes[ii,0].title.set_text(k)        
        axes[ii,1].title.set_text(k+' Avg. Cummulative')
    fig.suptitle('Error analysis')
    if not show_plot:
        plt.close(fig)
    
    # collect data        
    df1_ms_all = pd.concat(df1_ms, axis=1)
    df2_ms_all = pd.concat(df2_ms, axis=1)
    
    df1_ms_all_swapped = df1_ms_all.swaplevel(0, 1, 1).sort_index(axis=0)
    df2_ms_all_swapped = df2_ms_all.swaplevel(0, 1, 1).sort_index(axis=0)
    
    if not sort:
        # Add in fnames as indexes
        fnames = df['FN'].values
        df1_ms_all_swapped.index = fnames
        df2_ms_all_swapped.index = fnames
    
    keys = [ii[2] for ii in in_params[1:]]
    
    diff = {}
    diff_cum = {}
    for k in keys:
        diff[k] = df1_ms_all_swapped[k]
        diff_cum[k] = df2_ms_all_swapped[k]

    return diff, diff_cum, df

def compare_xy_against_GT_given_csvs_v2(in_params, on_header = 'FN'):
    ## sample : in_params = [(gt_csv, 'LBP', 'GT'), (al_csv, 'LBPc', 'AL'), (dl_csv, 'LBP', 'DL')]
    df = merge_data_multiple(in_params)
    df = ast_literal_eval_inplace(df, headers=df.columns[1:])
    
    gt_data = df[in_params[0][2]]
    in_data_comp = {}
    for (csv, in_header, out_header) in in_params[1:]:
        in_data_comp[out_header] = df[out_header]
    
    eucl_dist = lambda a,b : np.linalg.norm(np.vstack(a) - np.vstack(b), axis=1)
    absX_diff = lambda a,b : np.abs(np.vstack(a)[:,0] - np.vstack(b)[:,0])
    absY_diff = lambda a,b : np.abs(np.vstack(a)[:,1] - np.vstack(b)[:,1])
    metrics = {'Eucliean distance':eucl_dist,
               'Abs X diff':absX_diff,
               'Abs Y diff':absY_diff}

    fig, axes = plt.subplots(nrows=len(metrics), ncols=2)
    for ii,(k,v) in enumerate(metrics.items()):    
        df1_m, df2_m = compare_data_on_metrics(gt_data, in_data=in_data_comp, metric_func=v)
        df1_m.plot(ax=axes[ii,0])
        df2_m.plot(ax=axes[ii,1])
        axes[ii,0].title.set_text(k)        
        axes[ii,1].title.set_text(k+' Avg. Cummulative')        
    fig.suptitle('Error analysis')       
    return

def compare_xy_against_GT(df, GT_header, in_header, sort=True):
    eucl_dist = lambda a,b : np.linalg.norm(np.vstack(a) - np.vstack(b), axis=1)
    absX_diff = lambda a,b : np.abs(np.vstack(a)[:,0] - np.vstack(b)[:,0])
    absY_diff = lambda a,b : np.abs(np.vstack(a)[:,1] - np.vstack(b)[:,1])
    
    gt_data = df[GT_header]
    in_data = df[in_header]
    
    p_ed = eucl_dist(gt_data, in_data)
    p_ax = absX_diff(gt_data, in_data)
    p_ay = absY_diff(gt_data, in_data)
    
    if sort:
        p_ed = np.sort(p_ed)
        p_ax = np.sort(p_ax)
        p_ay = np.sort(p_ay)
    
    df = pd.DataFrame({'EuclideanDist':p_ed, 'AbsXDiff':p_ax, 'AbsYDiff':p_ay})
    df.plot()
    return

def compare_xy_against_GT_given_csvs(gt_csv, in_csv, header, in_header=None):
    if in_header is None:
        in_header = header
    p = merge_data(data1=gt_csv, data2=in_csv, data1_old_header=header, data1_new_header='GT', data2_old_header=in_header, data2_new_header='PRED')
    compare_xy_against_GT(p, GT_header='GT', in_header='PRED')
    return

def mark_featurepts_sample_config():
    sample = {'DL': {'label_color': (255, 0, 0),
        'pt_color': (255, 0, 0),
        'pt_thickness_type': 'normal'},
     'GT': {'label_color': (0, 0, 255),
            'pt_color': (0, 0, 255),
            'pt_thickness_type': 'normal'}}
    return sample

def merge_data_multiple(in_params, on_header='FN'):
    ## sample in_params : 
    # [(gt_csv, 'LBP', 'LBP1'), (al_csv, 'LBPc', 'LBP2'), (dl_csv, 'LBP', 'LBP3')]
    dfs = []
    for (csv, header, _) in in_params:
        df_i = read_data_from_any_source(csv)
        df_i = df_i[[on_header, header]]
        dfs.append(df_i)
    
    merged = pd.merge(dfs[0], dfs[1], on=on_header, how='inner')
    for df_i in dfs[2:]:
        merged = pd.merge(merged, df_i, on=on_header, how='inner')
    
    merged.columns = [merged.columns[0],] + [i[2] for i in in_params]
    return merged

def merge_data(data1,
               data2,
               data1_old_header,
               data1_new_header,
               data2_old_header,
               data2_new_header,
               on_header='FN'): 

    df1 = read_data_from_any_source(data1)
    df2 = read_data_from_any_source(data2)
    
    if data1_old_header not in df1:
        raise Exception('Header : '+data1_old_header + ' not found in first data.')
    
    if data2_old_header not in df2:
        raise Exception('Header : '+data2_old_header + ' not found in second data.')
    
    data1_old_header_tmp = data1_old_header+'_tmp1'
    data2_old_header_tmp = data1_old_header+'_tmp2'
    
    df1.rename(columns={data1_old_header:data1_old_header_tmp}, inplace=True)
    df2.rename(columns={data2_old_header:data2_old_header_tmp}, inplace=True)
    
    merged1 = df1.merge(df2, on=on_header)
    
    merged2 = merged1[[on_header, data1_old_header_tmp, data2_old_header_tmp]].copy()
    merged2.rename(columns={data2_old_header_tmp:data2_new_header, data1_old_header_tmp:data1_new_header}, inplace=True)
    merged3 = df_keep_valid_rows(merged2)
    ast_literal_eval_inplace(merged3, [data1_new_header, data2_new_header])
    return merged3

def mark_featurepts(data, indir, outdir, config, features=None, literal_eval=False):
    if features is None:
        # Default case for features, pick from config keys
        features = list(config.keys())
    
    df = read_data_from_any_source(data)
    dff = df_keep_valid_rows(df[['FN'] + features])
    
    if literal_eval:
        ast_literal_eval_inplace(dff, features)
    df_add_filepath_from_filename(dff,  indir, filepath_header='FP', filename_header='FN', drop_filename_header=True)
    
    mkdir(outdir)
    
    for iterID,(index, row) in enumerate(dff.iterrows()):
        iterinfo(dff['FP'].values, iterID)
        d = dict(row)
        fp = d['FP']
        im1 = read_image_from_any_source(fp)
        for feature in features:
            pt_xy = d[feature]
            
            mc = config[feature]
            pt_color, label_color, pt_thickness_type = mc['pt_color'], mc['label_color'], mc['pt_thickness_type']

            im1 = draw_featurept(im1, pt=pt_xy, label=feature, pt_thickness_type=pt_thickness_type, pt_color=pt_color, pt_size=15, x_offset=0, y_offset=20, label_fontsize=1, label_color=label_color, label_thickness=2)
        write_image_to_file(im1, os.path.join(outdir, os.path.basename(fp)))
    return

def read_data_from_any_source(data, print_message=False):
    if isinstance(data, pd.core.frame.DataFrame):
        if print_message:
            print('Input is a dataframe.')
        out_data = data
    elif isinstance(data, str):
        if data.split('.')[-1] != 'csv':
            raise Exception('Input file path is invalid (not csv)!')            
        if print_message:
            print('Input is a csv path.')
        if not os.path.exists(data):
            raise Exception('Data file path is invalid!')
        out_data = pd.read_csv(data)
    else:
        raise ValueError('Invalid data arg!')
    return out_data

def write_image_to_file(image, filepath):
    cv2.imwrite(filepath, image, [int(cv2.IMWRITE_JPEG_QUALITY), 100])
    return

def df_add_filepath_from_filename(df, indir, filepath_header='FP', filename_header='FN', force=False, drop_filename_header=False):
    if filepath_header in df and not force:
        raise Exception('filepath_header : '+ filepath_header + 'already exists.')

    if filename_header not in df:
        raise Exception('filename_header : '+ filename_header + 'does not exist.')
        
    df[filepath_header] = [os.path.join(indir, fn) for fn in df[filename_header]]
    
    if drop_filename_header:
        df.drop([filename_header], axis=1, inplace=True)
    return df

def df_keep_valid_rows(df):
    return df[~df.isnull().any(axis=1)]

def df_keep_valid_rows_based_on_filepath_header(df, filepath_header='FP'):
    if filepath_header not in df:
        raise Exception('filepath_header : '+ filepath_header + 'does not exist.')
    df_out = df[[os.path.exists(fp) for fp in df[filepath_header]]]
    return df_out
    
def validate_path_as_file_or_directory(path):
    if os.path.isdir(path):
        mode = 'directory'
    elif os.path.isfile(path):
        mode = 'file'
    else:
        raise Exception('Input path type could not be determined!')
    
    if not os.path.exists(path):
        raise Exception('Input path DOES NOT EXIST!')
    return mode

def filepaths_to_filenames(list_filepaths):
    return [os.path.basename(fp) for fp in list_filepaths]

def filenames_to_filepaths(list_filenames, indir):
    return [os.path.join(indir, fn) for fn in list_filenames]

def display_pandas_dataframes_expanded():
    np.set_printoptions(edgeitems=30, linewidth=100000, formatter=dict(float=lambda x: "%.3g" % x))
    pd.set_option('display.max_rows', 500)
    pd.set_option('display.max_columns', 500)
    pd.set_option('display.width', 1000)
    return

def analyze_prediction_from_df(df, GT_header='GT', Pred_header='Pred', category_outdir=None):
    success = (df[GT_header] == df[Pred_header]).mean()
    print('success = '+str(np.round(100*success,2))+'%')
    
    df_wrong_cases = df[df[GT_header] != df[Pred_header]].copy()

    comb_header = GT_header + '_' + Pred_header
    df_wrong_cases[comb_header] = df_wrong_cases[GT_header] + '_' + df_wrong_cases[Pred_header]
    
    if category_outdir is not None:
        categorize_from_df(df_wrong_cases, header=comb_header, outdir=category_outdir, print_progress=False, raise_warning_on_invalid_filepath=True)
    
    stats = df_wrong_cases[comb_header].value_counts()
    print('Stats per combination of mismatches :')    
    print(stats.to_string(index=True))
    
    confmat_textdisplay(df[GT_header], df[Pred_header])    
    return
    
# This needs df to have a header called "FP" for the filepaths
def categorize_from_df(df, header, outdir, print_progress=False, raise_warning_on_invalid_filepath=True):
    if 'FP' not in df:
        raise Exception('Needs a header called "FP" for the input filepaths of images.')

    newmkdir(outdir)
    unq = df[header].unique()
    
    for u in unq:
        outdir_catg = os.path.join(outdir, u)
        mkdir(outdir_catg)
            
    for ii,(index, row) in enumerate(df.iterrows()):
        if print_progress:
            iterinfo(df['FP'].values, ii)
        d = dict(row)
        src = d['FP']
        if raise_warning_on_invalid_filepath:
            if not os.path.exists(src):
                raise Exception('Path invalid : '+src)
        dst = os.path.join(os.path.join(outdir, d[header]), os.path.basename(src))
        copyfile(src, dst)
   
    print('Output data saved at : '+outdir)
    return

def rint(a):
    return int(np.round(a))

def get_categories(indir, category_name='Catg'):
    fpaths = listfiles_including_all_subdirs(indir)
    df = pd.DataFrame({'FN':[os.path.basename(fp) for fp in fpaths]})
    df[category_name] = [os.path.basename(os.path.dirname(fp)) for fp in fpaths]
    return df

def list_image_files(indir, RETURN_NAMES=False):
    fpaths = listfiles_including_all_subdirs(indir)
    goodexts = ['jpg', 'jpeg', 'png', 'bmp']
    
    fpaths_out = [fp for fp in fpaths if fp.split('.')[-1].lower() in goodexts]    
    if RETURN_NAMES:
        fnames_out = filepaths_to_filenames(fpaths_out)
        return fnames_out
    else:
        return fpaths_out

def batch_image_apply_given_fpaths(fpaths, func):
    print('Batch applying : '+str(func.__name__))
    for iterID,f in enumerate(fpaths):
        #iterinfo(fpaths, iterID)
        im = cv2.imread(f)
        cv2.imwrite(f, func(im), [int(cv2.IMWRITE_JPEG_QUALITY), 100])
    return  

def listfiles_including_all_subdirs(dirname):
    dirs = listallsubdirs(dirname)
    dirs.append(dirname)
    return np.concatenate([listfiles(d, fullpath=True) for d in dirs])

def listallsubdirs(dirname):
    subfolders= [f.path for f in os.scandir(dirname) if f.is_dir()]
    for dirname in list(subfolders):
        subfolders.extend(listallsubdirs(dirname))
    return subfolders

# Create window for displaying                    
def CreateWindow(name,image):
    cv2.namedWindow(name,cv2.WINDOW_NORMAL)
    cv2.setWindowProperty(name,cv2.WND_PROP_FULLSCREEN,cv2.WINDOW_FULLSCREEN)
    cv2.imshow(name,image)


# Display window
def DisplayWindow():
    while(1):
        k = cv2.waitKey(33)
        if k==27:    # Space key to stop
             cv2.destroyAllWindows()
             break
        elif k & 0xFF == ord('q') :
            cv2.destroyAllWindows()
            if plt.get_fignums():
                plt.close('all')
        else:  # normally -1 returned,so don't print it
            continue

def showim(dbg_img, window_name='Image'):
    if dbg_img.dtype == 'bool':
        CreateWindow(window_name,(dbg_img*255).astype(np.uint8))
    else:
        CreateWindow(window_name,dbg_img)
    DisplayWindow()

def listfiles_including_subdirs(indir):
    return np.concatenate([generate_fullpaths(i,listfiles(i)) for i in listsubdirs(indir)])

def copyfiles(indir, outdir):
    mkdir(outdir)
    fnames = listfiles(indir)
    fpaths = generate_fullpaths(indir, fnames)
    for f in fpaths:
        copyfile(f, os.path.join(outdir, os.path.basename(f)))
    return

def batch_image_apply(indir, func, outdir=None):
    print('Batch applying : '+str(func.__name__))
    fpaths = filepaths(indir)
    if outdir is None:
        outdir = indir
    else:
        mkdir(outdir)
    for iterID,f in enumerate(fpaths):
        iterinfo(fpaths, iterID)
        im = cv2.imread(f)
        cv2.imwrite(os.path.join(outdir, os.path.basename(f)), func(im))
    return  

def iterinfo(fpaths, iterID):
    fpath = fpaths[iterID]
    fname = os.path.basename(fpath)
    print('------------ '+str(iterID)+'/'+str(len(fpaths)-1)+' : '+fname)
    
def flipimg(fpath, writeback=True):
	if writeback:
	    cv2.imwrite(fpath, cv2.imread(fpath)[:,::-1], [int(cv2.IMWRITE_JPEG_QUALITY), 100])
	    return
	else:
	    return cv2.imread(fpath)[:,::-1]

def flipimgs(indir):
    fpaths = filepaths(indir)
    for ii,fpath in enumerate(fpaths):
        fname = os.path.basename(fpath)
        print('IterID : '+str(ii)+'/'+str(len(fpaths)-1)+' : '+fname)
        cv2.imwrite(fpath, cv2.imread(fpath)[:,::-1], [int(cv2.IMWRITE_JPEG_QUALITY), 100]) 
    return
    
def filepaths_from_subdirs(indir, ext='jpg'):
    return np.concatenate([filepaths(i,ext) for i in listsubdirs(indir)])

def confmat_textdisplay(y_true, y_pred, outpath='confmat.png'):
    labels = np.union1d(np.unique(y_true), np.unique(y_pred))
    C = confusion_matrix(y_true, y_pred, labels=labels)
    df_cm = pd.DataFrame(C, index=labels, columns=labels)    
    print('Confusion matrix (Ground truth as index and Predictions as columns) :')
    print(df_cm)
    return
    
def confmat(y_true, y_pred, outpath='confmat.png'):
    labels = np.union1d(np.unique(y_true), np.unique(y_pred))
    C = confusion_matrix(y_true, y_pred, labels=labels)
    df_cm = pd.DataFrame(C, index=labels, columns=labels)    
    print('Confusion matrix (Ground truth as index and Predictions as columns) :')
    print(df_cm)
    
    hmap = sn.heatmap(df_cm, cmap='Oranges', annot=True, annot_kws={"size": 28}, fmt='g')
    plt.xlabel('Prediction')
    plt.ylabel('GroundTruth')
    fig = hmap.get_figure()
    fig.set_size_inches(18.5, 12.5)
    fig.savefig(outpath)
    plt.close(fig)
    return

def regularize_images(indir, new_ext = 'jpg'):
    fn = listfiles(indir)
    fpaths = generate_fullpaths(indir,fn)
    for src in fpaths:
        dst = '.'.join(src.split('.')[:-1]) + '.'+new_ext
        img_data = cv2.imread(src)
        if img_data is None:
            print('Invalid file :'+src)
        os.remove(src)
        cv2.imwrite(dst, img_data, [int(cv2.IMWRITE_JPEG_QUALITY), 100])

def check_valid_paths(filepaths):
    return np.all([os.path.exists(f) for f in filepaths])

def list_file_extensions(dir1):
    return np.unique([f.split('.')[-1] for f in listfiles(dir1)])

def listsubdirs(dir1):
    return [ f.path for f in os.scandir(dir1) if f.is_dir() ]

def listfiles(dir1, fullpath=False):
    files = [f for f in os.listdir(dir1) if os.path.isfile(os.path.join(dir1, f))]
    if fullpath:
        files = [os.path.join(dir1,f) for f in files]
    return np.array(files)

def is_same_filenames(d1, d2):
    f1 = filenames(d1)
    f2 = filenames(d2)
    return np.array_equal(f1,f2)

def hstack_images(im1, im2, height='max'):
    M1,N1 = im1.shape[:2]
    M2,N2 = im2.shape[:2]
    
    if height=='first':
        N = int(N2*float(M1)/M2)
        hstackedimg = np.hstack((im1,cv2.resize(im2, (N,M1))))
    elif height=='max':
        M = max(M1, M2)
        N1_new = int(N1*float(M)/M1)
        N2_new = int(N2*float(M)/M)
        hstackedimg = np.hstack(( cv2.resize(im1, (N1_new,M)), cv2.resize(im2, (N2_new,M)) ))
    else:
        raise ValueError('Wrong height arg!')
    return hstackedimg


def hstack_files(f1, f2, outf, height='max'):
    im1 = cv2.imread(f1)
    im2 = cv2.imread(f2)    
    cv2.imwrite(outf, hstack_images(im1, im2, height=height), [int(cv2.IMWRITE_JPEG_QUALITY), 100])  


def categorize(indir, data, groupby_header, filename_header='FN', print_copy_progress = True):
    outdir = indir+'_'+groupby_header+'_categorized'  
    newmkdir(outdir)
    
    print('Stats :')
    print(data.groupby([groupby_header]).size())
    
    unq_catg = data[groupby_header].unique()
    for u in unq_catg:
        mask = data[groupby_header]==u
        dfm = data[mask]
        
        outdir_catg = os.path.join(outdir,str(u))
        copyfiles_into_dir(generate_fullpaths(indir, dfm[filename_header]),outdir_catg, print_progress=print_copy_progress)
    return

def read_tuple_header_as_array(data):
    return np.array([ast.literal_eval(d) for d in data])

def ast_literal_eval_inplace(df, headers):
    for h in headers:
        df[h] = [ast.literal_eval(i) for i in df[h]]
    return df

def add_prefix(indir, ext, prefix, sep = ''):
    fpaths = filepaths(indir, ext=ext)
    fnames = paths_to_fnames(fpaths)
    new_fpaths = [os.path.join(indir,prefix+sep+f) for f in fnames]
    for (f1,f2) in zip(fpaths, new_fpaths):
        os.rename(f1, f2)
    return

def filepaths_inclsubdirs(dirn, ext='jpg'):
    matches = []
    for root, dirnames, filenames in os.walk(dirn):
        for filename in fnmatch.filter(filenames, '*.'+ext):
            matches.append(os.path.join(root, filename))
    return matches

def save_list(L, csv_path):
    pd.Series(L).to_csv(csv_path, header=False, index=False)

def fixjppng_withopencvwriteback(dirn):
    for f in filepaths(dirn):
        cv2.imwrite(f,cv2.imread(f))

def copyfiles_based_on_match_fromtwodirs_into_third(dir_to_get_files, dir_to_filter_from, outdir, invert=False, ext='jpg', print_progress=False):
    # Find matches from dir_to_filter_from in dir_to_get_files and copy those from dir_to_get_files into outdir
    f1 = filenames(dir_to_get_files, ext=ext)
    f2 = filenames(dir_to_filter_from, ext=ext)
    copyfiles_into_dir(generate_fullpaths(dir_to_get_files, f1[np.isin(f1,f2,invert=invert)]),outdir, print_progress=print_progress)
    return
    
def setdiff(dir1, dir2, ext='jpg'):
    return np.setdiff1d(filenames(dir1,ext), filenames(dir2,ext))

def replace_string_in_filenames(dir1, postfix, ext='jpg', replacestr=''):
    f1 = filepaths(dir1, ext)
    for f in f1:
        os.rename(f,f.replace(postfix,replacestr)) 
    return


def fix_underscore_colon_issue_for_inputdir(dirn, ext):
    files = filepaths(dirn, ext=ext)
    for p in files:
        pp = fix_underscore_colon_issue(p)
        new_path = os.path.join(os.path.dirname(p), os.path.basename(pp))
        os.rename(p, new_path)
    return


def fix_dir_underscore_colon(d):
    files = filepaths(d, 'csv')
    fns = [fix_underscore_colon_issue(i) for i in paths_to_fnames(files)]
    out_files = generate_fullpaths(d, fns)
    for (i, j) in zip(files, out_files):
        os.rename(i, j)
    return


def savefnames_to_csv(d, csv_fpath, header='FN'):
    pd.DataFrame({header: paths_to_fnames(d)}).to_csv(csv_fpath, index=None)
    return


def diffdirs(dir1, dir2, ext='jpg'):
    return filter_paths_fnames_from_listar(filepaths(dir1, ext=ext), filenames(dir2, ext=ext), invert=True)


def newext(files, ext, out_format='array'):
    files = np.array(
        ['.'.join(os.path.splitext(f)[:-1]) + '.' + ext for f in files])
    if out_format == 'array' or out_format == 'a':
        return np.array(files)
    elif out_format == 'list' or out_format == 'l':
        return files


def fix_underscore_colon_issue(p):
        # p is input str
    n = len(p.split('.')[-1])
    if p[-n-4] == '_':
        p = p[:-n-4] + ':' + p[-n-3:]

    if p[-n-7] == '_':
        p = p[:-n-7] + ':' + p[-n-6:]
    return p


def __rmext_single(s):
    return '.'.join(s.split('.')[:-1])


def rmext(paths, out_format='array'):
    files = [__rmext_single(f) for f in paths]
    if out_format == 'array' or out_format == 'a':
        return np.array(files)
    elif out_format == 'list' or out_format == 'l':
        return files

def addext(paths, ext, out_format='array'):
    files = [f+'.'+ext for f in paths]
    if out_format == 'array' or out_format == 'a':
        return np.array(files)
    elif out_format == 'list' or out_format == 'l':
        return files

def __basename_woext(path):
    return rmext(os.path.basename(path))


def paths_to_fnames_woext(paths, out_format='array'):
    files = [__bas__basename_woextename_woext__basename_woext(
        f) for f in paths]
    if out_format == 'array' or out_format == 'a':
        return np.array(files)
    elif out_format == 'list' or out_format == 'l':
        return files


def filenames_woext(dirn, ext='jpg', out_format='array'):
    files = [__basename_woext(f) for f in filepaths(
        dirn, ext=ext, out_format='array')]
    if out_format == 'array' or out_format == 'a':
        return np.array(files)
    elif out_format == 'list' or out_format == 'l':
        return files


def choose_dir(local_dir=None, server_dir=None):
    if getpass.getuser() == 'diva':
        dirn = local_dir
    elif getpass.getuser() == 'ubuntu':
        dirn = server_dir
    else:
        raise Exception("Unidentified username!")
    return dirn


def initialize_csv(cols, csv_fname):
    df = pd.DataFrame(columns=cols)
    df.to_csv(csv_fname, index=None)
    return


def add_entry_to_csv(csv_fname, data_list):
    # data_list is a list of all entries
    df = pd.read_csv(csv_fname)
    cols = df.columns
    data_list = [[i] for i in data_list]
    df_entry = pd.DataFrame(dict(zip(cols, data_list)))
    df = pd.concat([df, df_entry], axis=0)
    df = df[cols]
    df.to_csv(csv_fname, index=None)
    return


def generate_final_output(a, out_format):
    if out_format == 'array' or out_format == 'a':
        return np.array(a)
    elif out_format == 'list' or out_format == 'l':
        return a


def generate_fullpaths(indir, fnames, out_format='array'):
    return generate_final_output([os.path.join(indir, fn) for fn in fnames], out_format)


def filtercsv(csv_fname, filterby, extractby, out_format='array'):
    df = pd.read_csv(csv_fname, dtype=str)
    files = df.loc[df[filterby].isnull(), extractby].tolist()
    if out_format == 'array' or out_format == 'a':
        return np.array(files)
    elif out_format == 'list' or out_format == 'l':
        return files1


def extract_specific_data_from_csv(csv_fname, filter_header, filter_value, extract_header):
    df_a = pd.read_csv(csv_fname)
    return df_a.loc[df_a[filter_header] == filter_value, extract_header].tolist()


def dirfilepaths_to_csv(dirn, csv_fname, colname="FN"):
    pd.DataFrame({colname: filepaths(dirn)}).to_csv(csv_fname, index=None)
    return

from tqdm import tqdm
def copyfiles_into_dir(files, outdir, print_progress=True, print_tqdm=False):
    outdir = os.path.realpath(outdir)
    mkdir(outdir)
    
    if print_tqdm:
        files_iter = tqdm(files)
        print_progress = False
    else:
        files_iter = files
    
    for src in files_iter:
        dst = os.path.join(outdir, os.path.basename(src))
        if os.path.exists(src) == 1:
            if print_progress == 1:
                print('--------------------------------------------------------------')
                print("Copy from : " + src)
                print("Copy to : " + dst)
            
            if src != dst:
                copyfile(src, dst)
    return

def mkdirs(dirns):
    for dirn in dirns:
        if not os.path.exists(dirn):
            os.makedirs(dirn)

def mkdir(dirn):
    if not os.path.exists(dirn):
        os.makedirs(dirn)

# Freshly create output dir
def newmkdir(P):
    if os.path.isdir(P):
        shutil.rmtree(P)    
    mkdir(P)
    
def filter_keep_correspondence(data1, data2, out_format='array'):
    f1 = rmext(paths_to_fnames(data1))
    f2 = rmext(paths_to_fnames(data2))

    f2_mask = np.isin(f2, f1)
    f2 = f2[f2_mask]

    sidx = f1.argsort()
    searched_idx = sidx[np.searchsorted(f1, f2, sorter=sidx)]
    files = [data1[idx] for idx in searched_idx]
    if out_format == 'array' or out_format == 'a':
        return np.array(files), f2_mask
    elif out_format == 'list' or out_format == 'l':
        return files, f2_mask


def filter_paths_fnames_from_listar(in_files, selected_files, invert=False, out_format='array'):
    data_fnames = paths_to_fnames(in_files)
    filter_fnames = paths_to_fnames(selected_files)

    # Remove file extensions
    data_fnames = ['.'.join(i.split('.')[:-1])
                   for i in paths_to_fnames(data_fnames)]
    filter_fnames = ['.'.join(i.split('.')[:-1])
                     for i in paths_to_fnames(filter_fnames)]

    mask = np.isin(data_fnames, filter_fnames)
    if invert == 1:
        mask = ~mask
    files = np.asarray(in_files)[mask].tolist()
    if out_format == 'array' or out_format == 'a':
        return np.array(files)
    elif out_format == 'list' or out_format == 'l':
        return files


def filter_paths_fnames_from_csv(in_files, csv_fname, out_format='array', invert=False):
    data_fnames = paths_to_fnames(in_files)
    filter_fnames = paths_to_fnames(readcsv_for_fnames(csv_fname))

    # Remove file extensions
    data_fnames = ['.'.join(i.split('.')[:-1])
                   for i in ostools.paths_to_fnames(data_fnames)]
    filter_fnames = ['.'.join(i.split('.')[:-1])
                     for i in ostools.paths_to_fnames(filter_fnames)]

    if invert == 0:
        files = np.asarray(in_files)[np.isin(
            data_fnames, filter_fnames)].tolist()
    else:
        files = np.asarray(in_files)[~np.isin(
            data_fnames, filter_fnames)].tolist()
    if out_format == 'array' or out_format == 'a':
        return np.array(files)
    elif out_format == 'list' or out_format == 'l':
        return files


def readcsv_for_fnames(csv_fname, colname=None, out_format='array'):
    if pd.read_csv(csv_fname).shape[1] == 1:
        print("One column data detected!")
        if pd.read_csv(csv_fname).columns[0].find('.') != -1:
            print("No header detected!")
            files = pd.read_csv(csv_fname, header=None).iloc[:, 0].tolist()
        else:
            print("Header detected!")
            files = pd.read_csv(csv_fname).iloc[:, 0].tolist()
    elif pd.read_csv(csv_fname).shape[1] > 1:
        print("More than one column data detected!")
        if colname is None:
            raise Exception("No column name provided!")
        else:
            print("Using provided colname")
            files = pd.read_csv(csv_fname).loc[:, colname].tolist()
    else:
        raise Exception("Bad CSV data!")
    if out_format == 'array' or out_format == 'a':
        return np.array(files)

def filepaths(dirn, ext='jpg', out_format='array'):
    dirn = os.path.realpath(dirn)
    files = sorted(glob(os.path.join(dirn+'/*.'+ext)))
    if out_format == 'array' or out_format == 'a':
        return np.array(files)
    elif out_format == 'list' or out_format == 'l':
        return files
    elif out_format == 'list' or out_format == 'l':
        return files

def paths_to_fnames(paths, out_format='array'):
    files = [os.path.basename(f) for f in paths]
    if out_format == 'array' or out_format == 'a':
        return np.array(files)
    elif out_format == 'list' or out_format == 'l':
        return files


def filepaths(dirn, ext='jpg', out_format='array'):
    dirn = os.path.realpath(dirn)
    files = sorted(glob(os.path.join(dirn+'/*.'+ext)))
    if out_format == 'array' or out_format == 'a':
        return np.array(files)
    elif out_format == 'list' or out_format == 'l':
        return files


def filenames(dirn, ext='jpg', out_format='array'):
    files = [os.path.basename(f) for f in filepaths(
        dirn, ext=ext, out_format='array')]
    if out_format == 'array' or out_format == 'a':
        return np.array(files)
    elif out_format == 'list' or out_format == 'l':
        return files


def filenames_to_csv(dirn, csv_fname):
    pd.DataFrame({'FN':filenames(dirn)}).to_csv(csv_fname, index=None)
    #pd.Series(filenames(dirn)).to_csv(csv_fname, index=None)
    return
