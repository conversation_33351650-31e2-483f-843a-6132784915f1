import numpy as np
import json
import os
from PIL import Image
from fastai.vision.all import load_learner


# Input path to image file
image_fpath = 'sample_imgs/Volkswagen_Golf_VIII_IMG_4023.jpg'

image = Image.open(image_fpath)

# color-model
modelpath = 'fastai_models/color.pkl'
learn = load_learner(modelpath, cpu=True)

# # color-model
def predict_car_color(img):
    img = np.array(img)
    out = learn.predict(img)

    label = out[0]
    score = np.round(float(out[2][int(out[1])]),2)
    return label, score

def generate_json_output(image_path, color_prediction, confidence_score):
    # Get image dimensions
    img = Image.open(image_path)
    width, height = img.size

    # Get filename
    filename = os.path.basename(image_path)

    # Create JSON structure
    json_output = {
        "info": {
            "year": "",
            "version": "",
            "description": "",
            "contributor": "",
            "url": "",
            "date_created": ""
        },
        "licenses": [
            {
                "url": "",
                "id": 1,
                "name": ""
            }
        ],
        "categories": [
            {
                "id": 1,
                "name": "holder",
                "supercategory": ""
            }
        ],
        "images": [
            {
                "id": 0,
                "license": 1,
                "file_name": filename,
                "height": height,
                "width": width,
                "date_captured": "null"
            }
        ],
        "annotations": [
            {
                "id": 1,
                "image_id": 0,
                "category_id": 1,
                "captions": [
                    {
                        "color": color_prediction.title()
                    }
                ]
            }
        ]
    }

    return json_output

carcolor, carcolor_score = predict_car_color(image)
print(f"Predicted color: {carcolor}, Confidence: {carcolor_score}")

# Generate JSON output
json_result = generate_json_output(image_fpath, carcolor, carcolor_score)

# Print formatted JSON
print("\nJSON Output:")
print(json.dumps(json_result, indent=4))

# Save to file
output_filename = f"{os.path.splitext(os.path.basename(image_fpath))[0]}_color_prediction.json"
with open(output_filename, 'w') as f:
    json.dump(json_result, f, indent=4)

print(f"\nJSON output saved to: {output_filename}")
