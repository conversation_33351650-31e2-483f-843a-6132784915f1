import numpy as np
from PIL import Image
from fastai.vision.all import load_learner


# Input path to image file
image_fpath = 'sample_imgs/Volkswagen_Golf_VIII_IMG_4023.jpg'

image = Image.open(image_fpath)

# color-model
modelpath = 'fastai_models/color.pkl'
learn = load_learner(modelpath, cpu=True)

# # color-model        
def predict_car_color(img):
    img = np.array(img)
    out = learn.predict(img)

    label = out[0]
    score = np.round(float(out[2][int(out[1])]),2)    
    return label, score

carcolor, carcolor_score = predict_car_color(image)
print(carcolor, carcolor_score)
