import numpy as np
import json
import os
import glob
from PIL import Image
from fastai.vision.all import load_learner
from tqdm import tqdm

# Load the color model once
print("Loading color model...")
modelpath = 'fastai_models/color.pkl'
learn = load_learner(modelpath, cpu=False)  # Use GPU if available
print("Model loaded successfully!")

def predict_car_color(img):
    """Predict car color from image"""
    img = np.array(img)
    out = learn.predict(img)
    
    label = out[0]
    score = np.round(float(out[2][int(out[1])]),2)    
    return label, score

def generate_json_output(image_path, color_prediction, confidence_score):
    """Generate JSON output in the required format"""
    # Get image dimensions
    try:
        img = Image.open(image_path)
        width, height = img.size
        img.close()
    except Exception as e:
        print(f"Error reading image {image_path}: {e}")
        return None
    
    # Get filename
    filename = os.path.basename(image_path)
    
    # Create JSON structure
    json_output = {
        "info": {
            "year": "",
            "version": "",
            "description": "",
            "contributor": "",
            "url": "",
            "date_created": ""
        },
        "licenses": [
            {
                "url": "",
                "id": 1,
                "name": ""
            }
        ],
        "categories": [
            {
                "id": 1,
                "name": "holder",
                "supercategory": ""
            }
        ],
        "images": [
            {
                "id": 0,
                "license": 1,
                "file_name": filename,
                "height": height,
                "width": width,
                "date_captured": "null"
            }
        ],
        "annotations": [
            {
                "id": 1,
                "image_id": 0,
                "category_id": 1,
                "captions": [
                    {
                        "color": color_prediction.title()
                    }
                ]
            } 
        ]
    }
    
    return json_output

def main():
    """Test with just a few images from one batch"""
    # Test with first batch and only first 3 images
    batch_dir = "data/color_sub_batch_62"
    output_dir = "test_outputs"
    
    # Create output directory
    os.makedirs(output_dir, exist_ok=True)
    
    # Find first 3 image files
    image_files = glob.glob(os.path.join(batch_dir, "*.jpg"))[:3]
    
    print(f"Testing with {len(image_files)} images from {batch_dir}")
    
    for image_path in tqdm(image_files, desc="Processing test images"):
        try:
            # Load and predict
            image = Image.open(image_path)
            color_prediction, confidence_score = predict_car_color(image)
            image.close()
            
            print(f"Image: {os.path.basename(image_path)} -> Color: {color_prediction} (confidence: {confidence_score})")
            
            # Generate JSON output
            json_result = generate_json_output(image_path, color_prediction, confidence_score)
            
            if json_result is not None:
                # Save JSON file with same name as image (but .json extension)
                image_filename = os.path.splitext(os.path.basename(image_path))[0]
                output_filename = f"{image_filename}.json"
                output_path = os.path.join(output_dir, output_filename)
                
                with open(output_path, 'w') as f:
                    json.dump(json_result, f, indent=4)
                
                print(f"Saved: {output_filename}")
                
        except Exception as e:
            print(f"Error processing {image_path}: {e}")
    
    print(f"\nTest completed! Check the {output_dir} directory for results.")

if __name__ == "__main__":
    main()
