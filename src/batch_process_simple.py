import numpy as np
import json
import os
import glob
from PIL import Image
from fastai.vision.all import load_learner
from tqdm import tqdm

def generate_json_output(image_path, color_prediction, confidence_score):
    """Generate JSON output in the required format"""
    # Get image dimensions
    try:
        img = Image.open(image_path)
        width, height = img.size
        img.close()
    except Exception as e:
        print(f"Error reading image {image_path}: {e}")
        return None
    
    # Get filename
    filename = os.path.basename(image_path)
    
    # Create JSON structure
    json_output = {
        "info": {
            "year": "",
            "version": "",
            "description": "",
            "contributor": "",
            "url": "",
            "date_created": ""
        },
        "licenses": [
            {
                "url": "",
                "id": 1,
                "name": ""
            }
        ],
        "categories": [
            {
                "id": 1,
                "name": "holder",
                "supercategory": ""
            }
        ],
        "images": [
            {
                "id": 0,
                "license": 1,
                "file_name": filename,
                "height": height,
                "width": width,
                "date_captured": "null"
            }
        ],
        "annotations": [
            {
                "id": 1,
                "image_id": 0,
                "category_id": 1,
                "captions": [
                    {
                        "color": color_prediction.title()
                    }
                ]
            } 
        ]
    }
    
    return json_output

def predict_car_color(img, learn):
    """Predict car color from image"""
    img = np.array(img)
    out = learn.predict(img)
    
    label = out[0]
    score = np.round(float(out[2][int(out[1])]),2)    
    return label, score

def process_single_batch(batch_name, max_images=None):
    """Process a single batch"""
    print(f"Processing batch: {batch_name}")
    
    # Load model
    print("Loading color model...")
    modelpath = 'fastai_models/color.pkl'
    learn = load_learner(modelpath, cpu=True)
    print("Model loaded successfully!")
    
    # Setup paths
    batch_dir = f"data/{batch_name}"
    output_dir = f"outputs/{batch_name}"
    
    # Create output directory
    os.makedirs(output_dir, exist_ok=True)
    
    # Find image files
    image_files = glob.glob(os.path.join(batch_dir, "*.jpg"))
    if max_images:
        image_files = image_files[:max_images]
    
    print(f"Found {len(image_files)} images to process")
    
    successful = 0
    failed = 0
    
    for image_path in tqdm(image_files, desc=f"Processing {batch_name}"):
        try:
            # Load and predict
            image = Image.open(image_path)
            color_prediction, confidence_score = predict_car_color(image, learn)
            image.close()
            
            # Generate JSON output
            json_result = generate_json_output(image_path, color_prediction, confidence_score)
            
            if json_result is not None:
                # Save JSON file with same name as image (but .json extension)
                image_filename = os.path.splitext(os.path.basename(image_path))[0]
                output_filename = f"{image_filename}.json"
                output_path = os.path.join(output_dir, output_filename)
                
                with open(output_path, 'w') as f:
                    json.dump(json_result, f, indent=4)
                
                successful += 1
            else:
                failed += 1
                
        except Exception as e:
            print(f"Error processing {image_path}: {e}")
            failed += 1
    
    print(f"Batch {batch_name} completed: {successful} successful, {failed} failed")
    return successful, failed

def main():
    """Process all batches or specific batch"""
    import sys
    
    if len(sys.argv) > 1:
        # Process specific batch
        batch_name = sys.argv[1]
        max_images = int(sys.argv[2]) if len(sys.argv) > 2 else None
        process_single_batch(batch_name, max_images)
    else:
        # Process all batches
        batch_dirs = [d for d in glob.glob("data/color_sub_batch_*") if os.path.isdir(d)]
        batch_dirs.sort()
        
        print(f"Found {len(batch_dirs)} batches to process")
        
        total_successful = 0
        total_failed = 0
        
        for batch_dir in batch_dirs:
            batch_name = os.path.basename(batch_dir)
            successful, failed = process_single_batch(batch_name)
            total_successful += successful
            total_failed += failed
        
        print(f"\nAll batches completed!")
        print(f"Total: {total_successful} successful, {total_failed} failed")

if __name__ == "__main__":
    main()
