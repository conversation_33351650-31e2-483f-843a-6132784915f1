import numpy as np
import os
from tqdm import tqdm
from PIL import Image
from fastai.vision.all import load_learner
import itworktools as wt


## Sample prelabel json
# [
#     {
#         "data": "https://asset.url/image-sample.jpg",
#         "classifications": [
#             {
#                 "schemaId": "a315d9ecaf2f101a1f5c256",
#                 "title": "Color",
#                 "answer": "Beige"
#             }
#         ]
#     }
# ]

indir = '/home/<USER>/Work/imerit_github_repos/production/selex_color/src/sample_imgs'
#indir = '/media/imerit/6353b67d-06d9-431f-94f3-c4d021585a38/projects_bkp/selex_car_color/retrain/selex_color/final2'

map1 = {
        'beige':'Beige',
        'unknown':'Unknown',
        'white':'White',
        'yellow':'Yellow',
        'black':'Black',
        'silver':'Silver',
        'gray':'Grey',
        'dark_red':'Dark_red',
        'dont_know':'Don’t_know',
        'red':'Red',
        'green':'Green',
        'blue':'Blue',
        }

fpaths = wt.list_image_files(indir)

# color-model
modelpath = 'fastai_models/color.pkl'
learn = load_learner(modelpath, cpu=True)

# # color-model        
def predict_car_color(img):
    img = np.array(img)
    out = learn.predict(img)
    label = out[0]
    score = np.round(float(out[2][int(out[1])]),2)    
    return label, score

data = []
for image_fpath in tqdm(fpaths):
    fn = os.path.basename(image_fpath)
    image = Image.open(image_fpath)
    
    carcolor, carcolor_score = predict_car_color(image)
    mapped_classname = map1[carcolor]    
    info_i = {
        'data':fn,
        'classifications':[{
            "schemaId": "a315d9ecaf2f101a1f5c256",
            "title": "Color",
            "answer": mapped_classname,
            }]
        }
    data.append(info_i)

import json
with open('prelabels_multiple.json', 'w') as f:
    json.dump(data, f)
