import numpy as np
import json
import os
import glob
from PIL import Image
from fastai.vision.all import load_learner
from tqdm import tqdm

# Load the color model once
print("Loading color model...")
modelpath = 'fastai_models/color.pkl'
learn = load_learner(modelpath, cpu=False)  # Use GPU if available
print("Model loaded successfully!")

def predict_car_color(img):
    """Predict car color from image"""
    img = np.array(img)
    out = learn.predict(img)
    
    label = out[0]
    score = np.round(float(out[2][int(out[1])]),2)    
    return label, score

def generate_json_output(image_path, color_prediction, confidence_score):
    """Generate JSON output in the required format"""
    # Get image dimensions
    try:
        img = Image.open(image_path)
        width, height = img.size
        img.close()
    except Exception as e:
        print(f"Error reading image {image_path}: {e}")
        return None
    
    # Get filename
    filename = os.path.basename(image_path)
    
    # Create JSON structure
    json_output = {
        "info": {
            "year": "",
            "version": "",
            "description": "",
            "contributor": "",
            "url": "",
            "date_created": ""
        },
        "licenses": [
            {
                "url": "",
                "id": 1,
                "name": ""
            }
        ],
        "categories": [
            {
                "id": 1,
                "name": "holder",
                "supercategory": ""
            }
        ],
        "images": [
            {
                "id": 0,
                "license": 1,
                "file_name": filename,
                "height": height,
                "width": width,
                "date_captured": "null"
            }
        ],
        "annotations": [
            {
                "id": 1,
                "image_id": 0,
                "category_id": 1,
                "captions": [
                    {
                        "color": color_prediction.title()
                    }
                ]
            } 
        ]
    }
    
    return json_output

def process_batch(batch_dir, output_dir):
    """Process all images in a batch directory"""
    batch_name = os.path.basename(batch_dir)
    print(f"\nProcessing batch: {batch_name}")
    
    # Create output directory for this batch
    batch_output_dir = os.path.join(output_dir, batch_name)
    os.makedirs(batch_output_dir, exist_ok=True)
    
    # Find all image files in the batch
    image_extensions = ['*.jpg', '*.jpeg', '*.png', '*.JPG', '*.JPEG', '*.PNG']
    image_files = []
    for ext in image_extensions:
        image_files.extend(glob.glob(os.path.join(batch_dir, ext)))
    
    if not image_files:
        print(f"No images found in {batch_dir}")
        return
    
    print(f"Found {len(image_files)} images in {batch_name}")
    
    # Process each image
    successful_predictions = 0
    failed_predictions = 0
    
    for image_path in tqdm(image_files, desc=f"Processing {batch_name}"):
        try:
            # Load and predict
            image = Image.open(image_path)
            color_prediction, confidence_score = predict_car_color(image)
            image.close()
            
            # Generate JSON output
            json_result = generate_json_output(image_path, color_prediction, confidence_score)
            
            if json_result is not None:
                # Save JSON file with same name as image (but .json extension)
                image_filename = os.path.splitext(os.path.basename(image_path))[0]
                output_filename = f"{image_filename}.json"
                output_path = os.path.join(batch_output_dir, output_filename)
                
                with open(output_path, 'w') as f:
                    json.dump(json_result, f, indent=4)
                
                successful_predictions += 1
            else:
                failed_predictions += 1
                
        except Exception as e:
            print(f"Error processing {image_path}: {e}")
            failed_predictions += 1
    
    print(f"Batch {batch_name} completed: {successful_predictions} successful, {failed_predictions} failed")

def main():
    """Main function to process all batches"""
    data_dir = "data"
    output_dir = "outputs"
    
    # Create main output directory
    os.makedirs(output_dir, exist_ok=True)
    
    # Find all batch directories
    batch_dirs = [d for d in glob.glob(os.path.join(data_dir, "*")) if os.path.isdir(d)]
    batch_dirs.sort()  # Sort for consistent processing order
    
    if not batch_dirs:
        print(f"No batch directories found in {data_dir}")
        return
    
    print(f"Found {len(batch_dirs)} batch directories:")
    for batch_dir in batch_dirs:
        print(f"  - {os.path.basename(batch_dir)}")
    
    # Process each batch
    total_batches = len(batch_dirs)
    for i, batch_dir in enumerate(batch_dirs, 1):
        print(f"\n{'='*50}")
        print(f"Processing batch {i}/{total_batches}")
        print(f"{'='*50}")
        process_batch(batch_dir, output_dir)
    
    print(f"\n{'='*50}")
    print("All batches processed successfully!")
    print(f"Results saved in: {output_dir}")
    print(f"{'='*50}")

if __name__ == "__main__":
    main()
